"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Menu, ChevronRight, BookOpen, FileText, Users, AlignJustify, Home } from "lucide-react"

interface SidebarProps {
  user: {
    name: string
    role: string
    avatar?: string
  }
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
}

export default function Sidebar({ user, isCollapsed, setIsCollapsed }: SidebarProps) {
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const pathname = usePathname()

  // Close mobile sidebar when route changes
  useEffect(() => {
    setIsMobileOpen(false)
  }, [pathname])

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen)
  }

  // แก้ไขส่วน menuItems เพื่อเพิ่มเมนูจัดการข้อสอบท้ายบท
  const menuItems = [
    {
      title: "หน้าหลัก",
      icon: <Home size={isCollapsed ? 24 : 20} />,
      path: "/admin",
      active: pathname === "/admin" || pathname === "/admin/",
    },
    {
      title: "จัดการคอร์ส",
      icon: <BookOpen size={isCollapsed ? 24 : 20} />,
      path: "/admin/courses",
      active: pathname.startsWith("/admin/courses"),
    },
    {
      title: "จัดการควิซ",
      icon: <FileText size={isCollapsed ? 24 : 20} />,
      path: "/admin/quiz",
      active: pathname.startsWith("/admin/quiz"),
    },
    {
      title: "จัดการข้อสอบท้ายบท",
      icon: <FileText size={isCollapsed ? 24 : 20} />,
      path: "/admin/exams",
      active: pathname.startsWith("/admin/exams"),
    },
    {
      title: "จัดการผู้ใช้",
      icon: <Users size={isCollapsed ? 24 : 20} />,
      path: "/admin/users",
      active: pathname.startsWith("/admin/users"),
    },
  ]

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && <div className="fixed inset-0 bg-black/50 z-40 md:hidden" onClick={toggleMobileSidebar} />}

      {/* Mobile Toggle Button */}
      <button
        className="fixed top-4 left-4 z-50 p-2 bg-white rounded-md shadow-md md:hidden"
        onClick={toggleMobileSidebar}
      >
        <AlignJustify size={24} />
      </button>

      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 h-full bg-white shadow-lg z-50 transition-all duration-300 
          ${isCollapsed ? "w-20" : "w-64"} 
          ${isMobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"}`}
      >
        {/* Sidebar Header */}
        <div
          className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} p-4 border-b h-[72px]`}
        >
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="relative w-10 h-10 rounded-full bg-gray-200 overflow-hidden">
                {user.avatar ? (
                  <Image src={user.avatar || "/placeholder.svg"} alt={user.name} layout="fill" objectFit="cover" />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <Users size={24} />
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-medium text-sm">{user.name}</h3>
                <p className="text-xs text-gray-500">• {user.role}</p>
              </div>
            </div>
          )}
          <button
            onClick={toggleSidebar}
            className={`text-gray-500 hover:text-gray-700 hidden md:flex items-center justify-center ${
              isCollapsed ? "h-10 w-10 rounded-md hover:bg-gray-100" : ""
            }`}
          >
            <Menu size={isCollapsed ? 24 : 20} />
          </button>
          <button onClick={toggleMobileSidebar} className="text-gray-500 hover:text-gray-700 md:hidden">
            <Menu size={20} />
          </button>
        </div>

        {/* Sidebar Menu */}
        <div className="py-4">
          {!isCollapsed && <p className="px-4 text-xs font-medium text-gray-500 uppercase mb-2">Dashboard menu</p>}
          <nav className="mt-2">
            {menuItems.map((item, index) => (
              <Link
                key={index}
                href={item.path}
                className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between"} 
                  px-4 py-3 text-sm transition-all duration-200
                  ${item.active ? "bg-gray-100 text-[#004C41] font-medium" : "text-gray-700 hover:bg-gray-50"}
                  ${isCollapsed ? "h-16" : ""}
                `}
              >
                <div className={`flex items-center ${isCollapsed ? "" : "space-x-3"}`}>
                  <span
                    className={`
                    ${item.active ? "text-[#004C41]" : "text-gray-500"} 
                    ${isCollapsed ? "p-1.5 rounded-md " : ""}
                  `}
                  >
                    {item.icon}
                  </span>
                  {!isCollapsed && <span>{item.title}</span>}
                </div>
                {!isCollapsed && <ChevronRight size={16} className="text-gray-400" />}
              </Link>
            ))}
          </nav>
        </div>
      </aside>
    </>
  )
}
