"use client"

import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { 
  Award, 
  Download, 
  Eye, 
  Calendar, 
  BookOpen, 
  ArrowLeft,
  X,
  ExternalLink
} from 'lucide-react'
import { getUserProgress } from '@/data/userProgress'
import { getCoursesData } from '@/data/allCourses'
import { getMedicalUsersData } from '@/data/allUsers'
import type { UserProgress } from '@/data/userProgress'
import type { CourseType } from '@/types/courses'
import type { MedicalUser } from '@/types/users'

interface CertificateWithCourse extends UserProgress {
  course: CourseType
}

const CertificateManagement = () => {
  const router = useRouter()
  const [user, setUser] = useState<MockUser | null>(null)
  const [certificates, setCertificates] = useState<CertificateWithCourse[]>([])
  const [selectedCertificate, setSelectedCertificate] = useState<CertificateWithCourse | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => {
      // Set mock user data
      setUser(mockUser)

      // Filter completed courses with certificates for the current user
      const completedWithCertificates = mockUserProgress.filter(
        (progress) => progress.userId === mockUser.id && progress.certificate.issued
      )

      // Combine with course data
      const certificatesWithCourses = completedWithCertificates.map((progress) => {
        const course = mockCourses.find((course) => course.id === progress.courseId)
        return {
          ...progress,
          course: course!
        }
      }).filter((cert) => cert.course) // Filter out any certificates without course data

      setCertificates(certificatesWithCourses)
      setLoading(false)
    }, 1000) // Simulate 1 second loading time

    return () => clearTimeout(timer)
  }, [router])

  const handleViewCertificate = (certificate: CertificateWithCourse) => {
    setSelectedCertificate(certificate)
    setIsModalOpen(true)
  }

  const handleDownloadCertificate = (certificate: CertificateWithCourse) => {
    if (certificate.certificate?.url) {
      // Create a temporary link element to trigger download
      const link = document.createElement('a')
      link.href = certificate.certificate.url
      link.download = `certificate-${certificate.course.name}-${user?.firstname}-${user?.lastname}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('th-TH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f9fafb] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#008268] mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังโหลดใบประกาศนียบัตร...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#f9fafb] w-full">
      <div className="flex w-full">
        {/* Sidebar */}
        <div className="w-[60px] fixed h-screen bg-[#008268] flex flex-col items-center py-6 z-30">
          <nav className="flex flex-col items-center gap-8 flex-grow">
            <Link
              href="/profile/dashboard"
              className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors"
            >
              <BookOpen size={24} />
            </Link>
            <Link
              href="/profile/dashboard/my-courses"
              className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors"
            >
              <BookOpen size={24} />
            </Link>
            <Link href="/profile/certificates" className="text-white p-3 rounded-xl bg-[#006e58]">
              <Award size={24} />
            </Link>
            <Link href="/profile/schedule" className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors">
              <Calendar size={24} />
            </Link>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
          <div className="w-full p-6">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center mb-2">
                <button
                  onClick={() => router.push("/profile/dashboard")}
                  className="mr-2 p-1 rounded-full hover:bg-gray-200"
                >
                  <ArrowLeft size={20} />
                </button>
                <h1 className="text-2xl font-bold text-gray-800">ใบประกาศนียบัตรของฉัน</h1>
              </div>
              <p className="text-gray-600">
                ใบประกาศนียบัตรที่คุณได้รับจากการเรียนจบคอร์สต่างๆ
              </p>
            </div>

            {/* Certificates Content */}
            {certificates.length === 0 ? (
              <div className="text-center py-16">
                <div className="mb-6">
                  <Award size={64} className="mx-auto text-gray-300" />
                </div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  ยังไม่มีใบประกาศนียบัตร
                </h3>
                <p className="text-gray-500 mb-6">
                  เมื่อคุณเรียนจบคอร์สที่มีการออกใบประกาศนียบัตร ใบประกาศนียบัตรจะแสดงที่นี่
                </p>
                <Link
                  href="/courses"
                  className="inline-flex items-center px-6 py-3 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
                >
                  <BookOpen size={20} className="mr-2" />
                  เริ่มเรียนคอร์ส
                </Link>
              </div>
            ) : (
              <>
                {/* Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-[#008268]/10 rounded-lg">
                        <Award className="h-6 w-6 text-[#008268]" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">ใบประกาศนียบัตรทั้งหมด</p>
                        <p className="text-2xl font-bold text-gray-900">{certificates.length}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-blue-100 rounded-lg">
                        <BookOpen className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">คอร์สที่เรียนจบ</p>
                        <p className="text-2xl font-bold text-gray-900">{certificates.length}</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-6 shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-3 bg-green-100 rounded-lg">
                        <Calendar className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">ใบประกาศนียบัตรล่าสุด</p>
                        <p className="text-sm font-bold text-gray-900">
                          {certificates.length > 0 && certificates[0].certificate?.date
                            ? formatDate(certificates[0].certificate.date)
                            : 'ไม่มีข้อมูล'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Certificates Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {certificates.map((certificate) => (
                    <div
                      key={certificate.courseId}
                      className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                    >
                      {/* Certificate Thumbnail */}
                      <div className="relative h-48 bg-gradient-to-br from-[#008268] to-[#006e58] rounded-t-lg flex items-center justify-center">
                        <div className="text-center text-white">
                          <Award size={48} className="mx-auto mb-2" />
                          <p className="text-sm font-medium">ใบประกาศนียบัตร</p>
                        </div>
                        {/* Course Image Overlay */}
                        {certificate.course.coverImage && (
                          <div className="absolute inset-0 bg-black/20 rounded-t-lg">
                            <Image
                              src={certificate.course.coverImage}
                              alt={certificate.course.name}
                              fill
                              className="object-cover rounded-t-lg opacity-30"
                            />
                          </div>
                        )}
                      </div>

                      {/* Certificate Info */}
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                          {certificate.course.name}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          ผู้สอน: {certificate.course.teacher.name}
                        </p>
                        <p className="text-sm text-gray-500 mb-4">
                          วันที่ออกใบประกาศนียบัตร: {' '}
                          {certificate.certificate?.date
                            ? formatDate(certificate.certificate.date)
                            : 'ไม่มีข้อมูล'
                          }
                        </p>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleViewCertificate(certificate)}
                            className="flex-1 flex items-center justify-center px-3 py-2 bg-[#008268] text-white text-sm rounded-md hover:bg-[#006e58] transition-colors"
                          >
                            <Eye size={16} className="mr-1" />
                            ดู
                          </button>
                          <button
                            onClick={() => handleDownloadCertificate(certificate)}
                            className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                          >
                            <Download size={16} className="mr-1" />
                            ดาวน์โหลด
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Certificate Modal */}
      {isModalOpen && selectedCertificate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  ใบประกาศนียบัตร
                </h3>
                <p className="text-sm text-gray-600">
                  {selectedCertificate.course.name}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleDownloadCertificate(selectedCertificate)}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download size={16} className="mr-1" />
                  ดาวน์โหลด
                </button>
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-4 overflow-auto max-h-[calc(90vh-120px)]">
              {selectedCertificate.certificate?.url ? (
                <div className="w-full">
                  {/* PDF Viewer for Desktop */}
                  <div className="hidden md:block">
                    <iframe
                      src={selectedCertificate.certificate.url}
                      className="w-full h-[600px] border rounded-lg"
                      title="Certificate PDF"
                    />
                  </div>

                  {/* Mobile View */}
                  <div className="md:hidden text-center py-8">
                    <Award size={64} className="mx-auto text-[#008268] mb-4" />
                    <h4 className="text-lg font-semibold mb-2">
                      {selectedCertificate.course.name}
                    </h4>
                    <p className="text-gray-600 mb-4">
                      ออกให้แก่: {user?.firstname} {user?.lastname}
                    </p>
                    <p className="text-sm text-gray-500 mb-6">
                      วันที่: {selectedCertificate.certificate?.date
                        ? formatDate(selectedCertificate.certificate.date)
                        : 'ไม่มีข้อมูล'
                      }
                    </p>
                    <a
                      href={selectedCertificate.certificate.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                    >
                      <ExternalLink size={16} className="mr-2" />
                      เปิดใบประกาศนียบัตร
                    </a>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">ไม่พบไฟล์ใบประกาศนียบัตร</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CertificateManagement
