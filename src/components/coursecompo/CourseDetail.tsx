"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { getCoursesData } from "@/data/allCourses"
import type { CourseType, Lesson } from "@/types/courses"
import TimeRaed from "@/components/time"
import Image from "next/image"



export default function CourseDetailsPage({
    params,
  }: {
    params: { id: string }
  }) {
  const [course, setCourse] = useState<CourseType | null>(null)
  const router = useRouter()

  useEffect(() => {
    const foundCourse = getCoursesData.find((c) => c.id === params.id)
    if (foundCourse) {
      setCourse(foundCourse)
    }
  }, [params.id])

  const handleStartLearning = () => {
    router.push(`/courses/${params.id}/learning`)
  }

  if (!course) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="h-8 bg-gray-200 w-64 mx-auto mb-4 rounded animate-pulse"></div>
          <div className="h-4 bg-gray-200 w-full max-w-2xl mx-auto rounded animate-pulse"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <button
            onClick={() => router.push("/courses")}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            กลับไปหน้าคอร์สทั้งหมด
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="h-64 bg-gray-200 relative">
            <img
              src={
                course.coverImage || `/placeholder.svg?height=300&width=800&query=${encodeURIComponent(course.name)}`
              }
              alt={course.name}
              className="w-full h-full object-cover"
            />
          </div>

          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-3xl font-bold">{course.name}</h1>
              <span className="bg-[#2FBCC1] text-white px-3 py-1 rounded-full text-sm">{course.level}</span>
            </div>

            <p className="text-gray-700 mb-6">{course.description}</p>

            <div className="flex items-center mb-6">
              <img
                src={course.teacher.avatar || "/placeholder.svg"}
                alt={course.teacher.name}
                className="w-12 h-12 rounded-full mr-4"
              />
              <div>
                <h3 className="font-semibold">{course.teacher.name}</h3>
                <p className="text-gray-600 text-sm">{course.teacher.description}</p>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-6">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 text-gray-500 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                  <span className="text-gray-700">
                    ระยะเวลา: <TimeRaed sec={course.time} />
                  </span>
                </div>
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 text-gray-500 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    ></path>
                  </svg>
                  <span className="text-gray-700">บทเรียน: {course.lesson.length} บท</span>
                </div>
              </div>

              <button
                onClick={handleStartLearning}
                className="w-full bg-[#2FBCC1] hover:bg-[#27a5a9] text-white py-3 rounded-md font-medium text-lg"
              >
                เริ่มเรียนบทเรียน
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-4">เนื้อหาบทเรียน</h2>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {course.lesson.map((lesson, index) => (
              <div key={lesson.id} className="border-b border-gray-200 last:border-b-0">
                <div className="p-4">
                  <div className="flex items-start">
                    <div className="bg-[#2FBCC1] text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 flex-shrink-0">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{lesson.name}</h3>
                      <p className="text-gray-600 text-sm">{lesson.description}</p>
                      <div className="mt-2 text-sm text-gray-500">
                        <TimeRaed sec={lesson.time} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Certificate Section - แสดงเฉพาะเมื่อ course.certify เป็น true */}
        {course.certify && (
          <div className="mt-8">
            <Image
              src="/e-med/img/certificate.png"
              width={240}
              height={100}
              alt="Certificate"
              className="shadow-xl drop-shadow-md md:hidden mt-10 mx-auto"
            />
            <div className="md:mt-[5vh] -mt-[1vh] p-6 border lg:w-[100vh] md:h-[11vh] ipad-air-landscape:h-[17vh] ipad-pro-landscape:w-[99vh] ipad-mini-landscape:w-[129vh] ipad-mini-landscape:h-[15vh] ipad-pro-landscape:h-[14vh] ipad-air-landscape:w-[121vh] lg:h-[12vh] ipad-pro:w-[72.3vh] rounded-lg bg-white flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold mb-2 lg:mt-1">ได้รับประกาศนียบัตร</h3>
                <p className="text-md text-gray-600">สามารถเพิ่มข้อมูลรับรองนี้ในโปรไฟล์ ประวัติย่อ หรือ CV ของคุณได้</p>
                <p className="text-md text-gray-600">แชร์บนโซเชียลมีเดียและระบุในรีวิวผลงานของคุณ</p>
              </div>
              {/* Certificate Image */}
              <div className="flex items-center">
                <Image
                  src="/e-med/img/certificate.png"
                  width={240}
                  height={100}
                  alt="Certificate"
                  className="shadow-xl drop-shadow-md ipad-pro:w-[22vh] ipad-pro:h-[14vh] md:w-[22vh] ipad-air-landscape:w-[32vh] ipad-air-landscape:h-[20vh] ipad-pro-landscape:w-[27vh] ipad-pro-landscape:h-[17vh] ipad-mini-landscape:h-[18vh] ipad-mini-landscape:w-[28vh] hidden md:flex"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
