
// components/CertificatePathway.jsx
import React from 'react';

type Status = 'completed' | 'in-progress' | 'locked';

const courses: { step: number; name: string; status: Status }[] = [
  { step: 1, name: 'Introduction to Pneumonia', status: 'completed' },
  { step: 2, name: 'Diagnostic Approaches', status: 'in-progress' },
  { step: 3, name: 'Treatment and Management', status: 'locked' },
];

// Status display mapping
const statusMap: Record<Status, { text: string; color: string; icon: JSX.Element }> = {
  completed: {
    text: 'Completed',
    color: 'text-green-600',
    icon: (
      <svg className="w-5 h-5 inline-block ml-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
      </svg>
    ),
  },
  'in-progress': {
    text: 'In Progress',
    color: 'text-yellow-500',
    icon: (
      <svg className="w-5 h-5 inline-block ml-1 animate-pulse" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" strokeDasharray="31.4" strokeDashoffset="31.4">
          {/* Just decorative */}
        </circle>
      </svg>
    ),
  },
  locked: {
    text: 'Locked',
    color: 'text-gray-400',
    icon: (
      <svg className="w-5 h-5 inline-block ml-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <rect width="14" height="10" x="5" y="11" rx="2" ry="2" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 11V7a3 3 0 10-6 0v4" />
      </svg>
    ),
  },
};

const CertificatePathway = () => {
  return (
    <div className="max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-3xl font-semibold text-blue-700 mb-4">Certificate Pathways</h1>
      <p className="text-gray-600 mb-6">
        Complete the required courses to earn your certificate.
      </p>

      <div className="border border-gray-200 rounded-md p-6 shadow-sm">
        <h2 className="text-xl font-bold text-blue-600 mb-4">Certificate: Treatment of Pneumonia</h2>

        <table className="w-full table-auto border-collapse">
          <thead>
            <tr className="bg-blue-50">
              <th className="border-b border-blue-200 px-4 py-2 text-left">Step</th>
              <th className="border-b border-blue-200 px-4 py-2 text-left">Course Name</th>
              <th className="border-b border-blue-200 px-4 py-2 text-left">Status</th>
            </tr>
          </thead>
          <tbody>
            {courses.map(({ step, name, status }) => {
              const statusInfo = statusMap[status];
              return (
                <tr key={step} className="hover:bg-gray-50">
                  <td className="border-b border-gray-100 px-4 py-3 font-medium">{step}</td>
                  <td className="border-b border-gray-100 px-4 py-3">{name}</td>
                  <td className={`border-b border-gray-100 px-4 py-3 font-semibold flex items-center ${statusInfo.color}`}>
                    {statusInfo.text}
                    {statusInfo.icon}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CertificatePathway;