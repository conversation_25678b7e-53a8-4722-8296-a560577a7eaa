"use client";

import Link from "next/link";

const Hero = () => {
  return (
    <section className="w-full lg:h-[100vh] ipad-pro:h-[60vh] bg-[#E4FBF3] py-16 px-4 flex justify-center">
      <div className="max-w-7xl w-full flex flex-col md:flex-row items-center gap-8">
        {/* Left Content */}
        <div className="flex-1 text-center md:text-left ]">
          <img
            alt="Medicine Image"
            src="/e-med/img/logo-1.png"
            className="lg:w-[50%]  lg:h-full object-cover">

          </img>
          <h1 className="lg:text-8xl ipad-pro:text-6xl mt-5 tracking-tight whitespace-nowrap font-bold text-[#008268] drop-shadow-xl">
            Care Academy
          </h1>
          <p className="lg:text-4xl ipad-pro:text-2xl font-bold whitespace-nowrap mt-5 text-gray-900 ">
            ระบบเพิ่มพูนความเชี่ยวชาญเพื่อการพยาบาล
          </p>
          <p className="lg:text-2xl ipad-pro:text-xl text-bold  text-gray-600 mt-5">โรงพยาบาลสินแพทย์</p>
          <div className="mt-7 flex justify-center md:justify-start gap-4">
            <button className="bg-[#4F62A8] text-white text-xl px-6 py-3 rounded-lg shadow-md font-bold">
              เรียนคอร์ส
            </button>
            <button className="bg-[#6FB5A7] text-white px-6 text-xl py-3 rounded-lg shadow-md font-bold">
              สมัครสมาชิก
            </button>
          </div>
        </div>

        {/* Right Image */}
        <div className="relative lg:left-[10vh] lg:top-[5vh] ipad-pro:top-[3vh] ipad-pro:left-[1vh] lg:flex-1 lg:flex lg:justify-center ">
          <div className="absolute lg:w-[10vh] lg:h-[10vh] ipad-pro:w-[5vh] ipad-pro:h-[5vh] bg-[#F2A1B4] shadow-[inset_0_5px_10px_#a16472] rounded-full  lg:-top-[2vh] lg:left-[10vh]"></div>
          <div className="relative lg:w-[50vh] lg:h-[50vh] ipad-pro:w-[25vh] ipad-pro:h-[25vh] bg-[#008268] shadow-[inset_0_10px_20px_#004d3b] rounded-full flex items-center justify-center overflow-visible">
            <img
              alt="Medicine Image"
              src="/e-med/img/medicine.png"
              className="absolute lg:bottom-[-10%] lg:left-[-7%] lg:w-[170%] lg:h-[107%] ipad-pro:w-[100%] ipad-pro:h-[100%] "
            ></img>
          </div>

          {/* Decorative Circles */}
          <div className="absolute lg:w-[16vh] lg:h-[16vh] ipad-pro:w-[8vh] ipad-pro:h-[8vh] bg-[#293C97] shadow-[0_5px_10px_rgba(0,0,0,0.1),_inset_0_-3px_5px_rgba(0,0,0,0.5),_inset_0_3px_5px_rgba(255,255,255,0.1)] rounded-full lg:-top-[2vh] lg:right-[5vh]"></div>
          <div className="absolute lg:w-[7vh] lg:h-[7vh] ipad-pro:w-[4vh] ipad-pro:h-[4vh] bg-[#6FB5A7] rounded-full shadow-[0_2px_5px_rgba(0,0,0,0.1),_inset_0_-3px_5px_rgba(0,0,0,0.3),_inset_0_3px_5px_rgba(255,255,255,0.1)] lg:bottom-[10vh] lg:left-[-7vh] ipad-pro:top-[16vh] ipad-pro:left-[0.1vh]"></div>
          <div className="absolute lg:w-[12vh] lg:h-[12vh] ipad-pro:w-[7vh] ipad-pro:h-[7vh] bg-[#008268] rounded-full shadow-[0_2px_5px_rgba(0,0,0,0.1),_inset_0_-3px_5px_rgba(0,0,0,0.3),_inset_0_3px_5px_rgba(255,255,255,0.1)] lg:bottom-[40vh] lg:left-[-4vh] ipad-pro:right-[5vh] ipad-pro:top-[0.1vh]"></div>
          <div className="absolute lg:w-[5vh] lg:h-[5vh] ipad-pro:w-[2vh] ipad-pro:h-[2vh] bg-[#4F62A8]  rounded-full shadow-[inset_0_10px_10px_#293C97]  lg:bottom-[25vh] lg:left-[-2vh] ipad-pro:left-[2vh] ipad-pro:top-[10vh]"></div>
          <div className="absolute lg:w-[11vh] lg:h-[11vh] bg-[#F2A1B4] ipad-pro:h-[7vh] ipad-pro:w-[7vh] shadow-[inset_0_5px_10px_#a16472] rounded-full  lg:top-[38vh] lg:left-[42vh] ipad-pro:top-[18vh] ipad-pro:left-[23vh]"></div>
        </div>
      </div>

    </section>

  );
};

export default Hero;
