import AdminLayout from "@/components/admin/layout"
import { Users, BookOpen, FileText } from "lucide-react"

export default function AdminDashboardPage() {
  const stats = [
    { title: "ผู้ใช้ทั้งหมด", value: "245", icon: <Users size={24} className="text-blue-500" /> },
    { title: "คอร์สทั้งหมด", value: "12", icon: <BookOpen size={24} className="text-green-500" /> },
    { title: "งานวิจัย", value: "8", icon: <FileText size={24} className="text-purple-500" /> },
  ]

  return (
    <AdminLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">แดชบอร์ด</h1>
        <p className="text-gray-600">ยินดีต้อนรับสู่ระบบจัดการ E-MED LEARNING</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                <h3 className="text-3xl font-bold text-gray-800 mt-1">{stat.value}</h3>
              </div>
              <div className="p-3 bg-gray-50 rounded-full">{stat.icon}</div>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
        <h2 className="text-lg font-medium text-gray-800 mb-4">กิจกรรมล่าสุด</h2>
        <div className="space-y-4">
          {[1, 2, 3].map((_, index) => (
            <div key={index} className="flex items-start pb-4 border-b border-gray-100 last:border-0 last:pb-0">
              <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0">
                <Users size={20} className="text-gray-500" />
              </div>
              <div>
                <p className="text-sm font-medium">ผู้ใช้ใหม่ได้ลงทะเบียนเข้าสู่ระบบ</p>
                <p className="text-xs text-gray-500 mt-1">2 ชั่วโมงที่แล้ว</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </AdminLayout>
  )
}
