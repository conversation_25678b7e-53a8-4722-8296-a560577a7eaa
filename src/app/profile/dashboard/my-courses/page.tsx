"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { BookOpen, Award, Calendar, ChevronRight, Filter, CheckCircle, Circle, Search, ArrowLeft } from "lucide-react"
import { getMedicalUsersData } from "@/data/allUsers"
import { getUserProgress } from "@/data/userProgress"
import { getCoursesData } from "@/data/allCourses"
import Navbar from "@/components/headers"
import CourseCard from "@/components/progressCard"

// นำเข้า Types จากไฟล์ types ทั้งหมด
import type { MedicalUser } from "@/types/users"
import type { UserProgress, LearningStatus } from "@/types/progress"

// ประเภทของการกรองคอร์ส
type FilterType = "all" | LearningStatus

export default function MyCoursesPage() {
  const router = useRouter()
  const [user, setUser] = useState<MedicalUser | null>(null)
  const [userProgress, setUserProgress] = useState<UserProgress[]>([])
  const [filteredProgress, setFilteredProgress] = useState<UserProgress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentFilter, setCurrentFilter] = useState<FilterType>("all")
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    // ตรวจสอบว่ามีการ login หรือไม่
    const userId = localStorage.getItem("userId")
    if (!userId) {
      router.push("/login")
      return
    }

    // ดึงข้อมูลผู้ใช้
    const userData = getMedicalUsersData.find((user) => user.id === userId)
    if (userData) {
      setUser(userData)

      // ดึงข้อมูลความก้าวหน้าในการเรียน
      const progress = getUserProgress(userId)
      // กรองเฉพาะคอร์สที่กำลังเรียนหรือเรียนจบแล้ว
      const activeProgress = progress.filter((p) => p.status === "in_progress" || p.status === "completed")
      setUserProgress(activeProgress)
      setFilteredProgress(activeProgress) // เริ่มต้นแสดงทั้งหมด
    }

    setIsLoading(false)
  }, [router])

  // กรองคอร์สตามสถานะ
  useEffect(() => {
    let filtered = [...userProgress]

    // กรองตามสถานะ
    if (currentFilter !== "all") {
      filtered = filtered.filter((progress) => progress.status === currentFilter)
    }

    // กรองตามคำค้นหา
    if (searchQuery) {
      filtered = filtered.filter((progress) => {
        const course = getCoursesData.find((c) => c.id === progress.courseId)
        if (!course) return false
        return course.name.toLowerCase().includes(searchQuery.toLowerCase())
      })
    }

    setFilteredProgress(filtered)
  }, [currentFilter, userProgress, searchQuery])

  // แปลงข้อความสถานะเป็นภาษาไทย
  const getStatusText = (status: LearningStatus): string => {
    switch (status) {
      case "completed":
        return "เรียนจบแล้ว"
      case "in_progress":
        return "กำลังเรียน"
      case "not_started":
        return "ยังไม่ได้เริ่ม"
      default:
        return ""
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#008268]"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">ไม่พบข้อมูลผู้ใช้</h1>
          <p className="text-gray-600 mb-6">กรุณาเข้าสู่ระบบเพื่อดูข้อมูลคอร์สเรียน</p>
          <button
            onClick={() => router.push("/login")}
            className="px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58]"
          >
            เข้าสู่ระบบ
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f9fafb] w-full">
        <div className="flex w-full">
          {/* Sidebar */}
          <div className="w-[60px] fixed h-screen bg-[#008268] flex flex-col items-center py-6 z-30">
            <nav className="flex flex-col items-center gap-8 flex-grow">
              <Link
                href="/profile/dashboard"
                className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors"
              >
                <BookOpen size={24} />
              </Link>
              <Link href="/profile/my-courses" className="text-white p-3 rounded-xl bg-[#006e58]">
                <BookOpen size={24} />
              </Link>
              <Link
                href="/profile/certificates"
                className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors"
              >
                <Award size={24} />
              </Link>
              <Link href="/profile/schedule" className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors">
                <Calendar size={24} />
              </Link>
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
            <div className="w-full p-6">
              {/* Header */}
              <div className="mb-8">
                <div className="flex items-center mb-2">
                  <button
                    onClick={() => router.push("/profile/dashboard")}
                    className="mr-2 p-1 rounded-full hover:bg-gray-200"
                  >
                    <ArrowLeft size={20} />
                  </button>
                  <h1 className="text-2xl font-bold text-gray-800">คอร์สเรียนของฉัน</h1>
                </div>
                <p className="text-gray-600">จัดการและติดตามความก้าวหน้าในการเรียนของคุณ</p>
              </div>

              {/* Search and Filter */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div className="relative w-full md:w-80">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="ค้นหาคอร์สเรียน..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 border bg-white border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-[#008268]"
                  />
                </div>

                <div className="relative">
                  <button
                    onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                    className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <Filter size={16} />
                    <span>{currentFilter === "all" ? "ทั้งหมด" : getStatusText(currentFilter as LearningStatus)}</span>
                    <ChevronRight
                      size={16}
                      className={`transition-transform ${showFilterDropdown ? "rotate-90" : ""}`}
                    />
                  </button>

                  {showFilterDropdown && (
                    <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10">
                      <div className="py-1">
                        <button
                          onClick={() => {
                            setCurrentFilter("all")
                            setShowFilterDropdown(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100"
                        >
                          {currentFilter === "all" ? (
                            <CheckCircle size={16} className="mr-2 text-green-600" />
                          ) : (
                            <Circle size={16} className="mr-2 text-gray-300" />
                          )}
                          ทั้งหมด
                        </button>
                        <button
                          onClick={() => {
                            setCurrentFilter("completed")
                            setShowFilterDropdown(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100"
                        >
                          {currentFilter === "completed" ? (
                            <CheckCircle size={16} className="mr-2 text-green-600" />
                          ) : (
                            <Circle size={16} className="mr-2 text-gray-300" />
                          )}
                          เรียนจบแล้ว
                        </button>
                        <button
                          onClick={() => {
                            setCurrentFilter("in_progress")
                            setShowFilterDropdown(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100"
                        >
                          {currentFilter === "in_progress" ? (
                            <CheckCircle size={16} className="mr-2 text-green-600" />
                          ) : (
                            <Circle size={16} className="mr-2 text-gray-300" />
                          )}
                          กำลังเรียน
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Course Grid */}
              {filteredProgress.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 md:gap-6 w-full">
                  {filteredProgress.map((progress) => {
                    const course = getCoursesData.find((c) => c.id === progress.courseId)
                    if (!course) return null

                    return (
                      <CourseCard
                        key={progress.courseId}
                        id={progress.courseId}
                        name={course.name}
                        teacherName={course.teacher.name}
                        coverImage={course.coverImage || ""}
                        progress={progress.progress}
                        completedLessons={progress.completedLessons}
                        totalLessons={progress.totalLessons}
                        duration={course.time}
                        status={progress.status}
                      />
                    )
                  })}
                </div>
              ) : (
                <div className="bg-white rounded-xl p-8 text-center w-full">
                  <div className="mb-4">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                      <BookOpen size={32} className="text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">ไม่พบคอร์สเรียน</h3>
                    <p className="text-gray-600 mb-6">
                      {searchQuery
                        ? "ไม่พบคอร์สเรียนที่ตรงกับคำค้นหา"
                        : currentFilter === "all"
                          ? "คุณยังไม่มีคอร์สที่กำลังเรียนหรือเรียนจบแล้ว"
                          : getStatusText(currentFilter as LearningStatus) === "เรียนจบแล้ว"
                            ? "คุณยังไม่มีคอร์สที่เรียนจบ"
                            : "คุณยังไม่มีคอร์สที่กำลังเรียน"}
                    </p>
                    <Link
                      href="/courses"
                      className="inline-block px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                    >
                      ดูคอร์สเรียนทั้งหมด
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <style jsx global>{`
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
  
          /* iPad Pro และอุปกรณ์ขนาดกลาง */
          @media (min-width: 768px) and (max-width: 1024px) {
            .grid-cols-2 {
              grid-template-columns: repeat(2, minmax(0, 1fr));
            }
    
            /* เพิ่ม padding ให้กับ content เพื่อให้มีพื้นที่มากขึ้น */
            .p-6 {
              padding: 1.25rem;
            }
    
            /* ปรับขนาด gap ระหว่างการ์ด */
            .gap-6 {
              gap: 1rem;
            }
          }
  
          /* สำหรับ iPad Pro แนวนอน */
          @media (min-width: 1024px) and (max-width: 1366px) {
            .grid-cols-3 {
              grid-template-columns: repeat(3, minmax(0, 1fr));
            }
          }
        `}</style>
      </div>
    </>
  )
}
