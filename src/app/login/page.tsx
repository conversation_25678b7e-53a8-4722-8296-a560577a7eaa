"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import Navbar from "@/components/headers"
import { getMedicalUsersData } from "@/data/allUsers"

export default function LoginPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  })

  const [errors, setErrors] = useState({
    email: "",
    password: "",
  })

  const [loginError, setLoginError] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Reset errors
    setLoginError("")

    // Basic validation
    const newErrors: any = {}
    if (!formData.email) newErrors.email = "กรุณากรอกอีเมล"
    if (!formData.password) newErrors.password = "กรุณากรอกรหัสผ่าน"

    setErrors(newErrors)

    // If no errors, proceed with login logic
    if (Object.keys(newErrors).length === 0) {
      // ตรวจสอบข้อมูลกับ data ที่มีอยู่
      const user = getMedicalUsersData.find(
        (user) => user.email === formData.email && user.password === formData.password,
      )

      if (user) {
        // Login สำเร็จ
        console.log("Login successful:", user)

        // เก็บข้อมูลผู้ใช้ใน localStorage
        localStorage.setItem("userId", user.id)

        // นำผู้ใช้ไปยังหน้า dashboard
        router.push("/profile/dashboard")
      } else {
        // Login ไม่สำเร็จ
        setLoginError("อีเมลหรือรหัสผ่านไม่ถูกต้อง")
      }
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#D0E2DF] flex justify-center items-center">
        {/* Main Content */}
        <div className="w-full max-w-4xl">
          <div className="bg-white lg:w-[100vh] lg:h-[60vh] rounded-2xl overflow-hidden shadow-xl flex flex-col md:flex-row">
            {/* Left Side - Background Image */}
            <div className="relative hidden md:block w-1/2">
              <div className="absolute inset-0 bg-[#004C41] opacity-75 z-10" />
              <Image
                src="/e-med/img/hospital_bg.png"
                alt="Hospital Background"
                layout="fill"
                objectFit="cover"
                className="object-cover z-0"
                priority
              />
              <div className="absolute bottom-0 left-0 p-4 z-20">
                <h1 className="text-white p-5 text-5xl font-extrabold leading-tight shadow-foreground">
                  E-MED
                  <br />
                  LEARNING
                </h1>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="w-full md:w-1/2 p-8 flex flex-col h-full">
              <h2 className="text-3xl font-bold text-center mt-10 mb-5">เข้าสู่ระบบ</h2>

              {loginError && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md mb-4">
                  {loginError}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6 flex flex-col h-full">
                <div className="flex-grow space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      อีเมล
                    </label>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]"
                    />
                    {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                  </div>

                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                      รหัสผ่าน
                    </label>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      required
                      value={formData.password}
                      onChange={handleChange}
                      className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]"
                    />
                    {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
                  </div>

                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        id="rememberMe"
                        name="rememberMe"
                        type="checkbox"
                        checked={formData.rememberMe}
                        onChange={handleChange}
                        className="h-4 w-4 text-[#008268] focus:ring-[#2FBCC1] border-gray-300 rounded"
                      />
                      <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700 ">
                        จดจำฉัน
                      </label>
                    </div>
                    <div className="text-sm">
                      <Link href="/forgot-password" className="font-medium text-[#008268] hover:text-[#6aafa1]">
                        ลืมรหัสผ่าน
                      </Link>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  className="w-full bg-[#008268] hover:bg-[#6aafa1]/90 text-white font-bold text-lg py-2 px-4 rounded-md lg:mt-auto"
                >
                  เข้าสู่ระบบ
                </button>
              </form>

              <div className="text-center mt-4">
                <p className="text-sm text-gray-600">
                  ยังไม่เป็นสมาชิก?{" "}
                  <Link href="/register" className="font-medium text-[#008268] hover:text-[#6aafa1]">
                    สมัครสมาชิก
                  </Link>
                </p>
              </div>

              {/* ข้อมูลสำหรับการทดสอบ */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-xs text-gray-500 text-center">ข้อมูลสำหรับการทดสอบ:</p>
                <div className="grid grid-cols-2 gap-2 mt-2 text-xs text-gray-500">
                  <div>
                    <p>นักศึกษา:</p>
                    <p><EMAIL></p>
                    <p>student123</p>
                  </div>
                  <div>
                    <p>อาจารย์:</p>
                    <p><EMAIL></p>
                    <p>password123</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
